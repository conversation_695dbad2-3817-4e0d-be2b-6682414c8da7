// node_modules/@mui/x-date-pickers/internals/utils/warning.js
var buildWarning = (message, gravity = "warning") => {
  let alreadyWarned = false;
  const cleanMessage = Array.isArray(message) ? message.join("\n") : message;
  return () => {
    if (!alreadyWarned) {
      alreadyWarned = true;
      if (gravity === "error") {
        console.error(cleanMessage);
      } else {
        console.warn(cleanMessage);
      }
    }
  };
};

export {
  buildWarning
};
//# sourceMappingURL=chunk-RW3DMCEO.js.map
