import express from 'express';
import cors from 'cors';
import pkg from 'pg';
const { Pool } = pkg;
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import cron from 'node-cron';
import dotenv from 'dotenv';
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Database connection (PostgreSQL)
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'subscription_management',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
});

// In-memory storage for development (replace with database in production)
let subscriptions = [
  {
    id: '1',
    customerName: 'أحمد محمد',
    networkName: 'شبكة الرياض',
    cloudName: 'كلاود 1',
    port: '8080',
    subscriptionType: 'monthly',
    amount: 100,
    startDate: '2024-01-01',
    endDate: '2024-02-01',
    status: 'active',
    isPaid: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    customerName: 'فاطمة علي',
    networkName: 'شبكة جدة',
    cloudName: 'كلاود 2',
    port: '8081',
    subscriptionType: 'annual',
    amount: 1000,
    startDate: '2023-12-01',
    endDate: '2024-12-01',
    status: 'active',
    isPaid: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

let invoices = [
  {
    id: '1',
    subscriptionId: '1',
    customerName: 'أحمد محمد',
    amount: 100,
    issueDate: '2024-01-01',
    dueDate: '2024-01-31',
    status: 'paid',
    createdAt: new Date().toISOString(),
  },
  {
    id: '2',
    subscriptionId: '2',
    customerName: 'فاطمة علي',
    amount: 1000,
    issueDate: '2024-01-01',
    dueDate: '2024-01-31',
    status: 'unpaid',
    createdAt: new Date().toISOString(),
  },
];

let notifications = [
  {
    id: '1',
    subscriptionId: '1',
    customerName: 'أحمد محمد',
    type: 'expiry_warning',
    message: 'اشتراك أحمد محمد سينتهي خلال 7 أيام',
    isRead: false,
    createdAt: new Date().toISOString(),
  },
  {
    id: '2',
    subscriptionId: '2',
    customerName: 'فاطمة علي',
    type: 'payment_due',
    message: 'فاتورة فاطمة علي مستحقة الدفع',
    isRead: false,
    createdAt: new Date().toISOString(),
  },
];

// Helper functions
const calculateEndDate = (startDate, subscriptionType) => {
  const start = new Date(startDate);
  switch (subscriptionType) {
    case 'monthly':
      start.setMonth(start.getMonth() + 1);
      break;
    case 'semi-annual':
      start.setMonth(start.getMonth() + 6);
      break;
    case 'annual':
      start.setFullYear(start.getFullYear() + 1);
      break;
  }
  return start.toISOString().split('T')[0];
};

const updateSubscriptionStatus = (subscription) => {
  const now = new Date();
  const endDate = new Date(subscription.endDate);
  const daysUntilExpiry = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
  
  if (daysUntilExpiry < 0) {
    subscription.status = 'expired';
  } else if (daysUntilExpiry <= 7) {
    subscription.status = 'warning';
  } else {
    subscription.status = 'active';
  }
  
  return subscription;
};

// Routes

// Subscriptions
app.get('/api/subscriptions', (req, res) => {
  const updatedSubscriptions = subscriptions.map(updateSubscriptionStatus);
  res.json(updatedSubscriptions);
});

app.get('/api/subscriptions/:id', (req, res) => {
  const subscription = subscriptions.find(s => s.id === req.params.id);
  if (!subscription) {
    return res.status(404).json({ error: 'Subscription not found' });
  }
  res.json(updateSubscriptionStatus(subscription));
});

app.post('/api/subscriptions', (req, res) => {
  const { customerName, networkName, cloudName, port, subscriptionType, amount, startDate } = req.body;
  
  const newSubscription = {
    id: (subscriptions.length + 1).toString(),
    customerName,
    networkName,
    cloudName,
    port,
    subscriptionType,
    amount,
    startDate,
    endDate: calculateEndDate(startDate, subscriptionType),
    status: 'active',
    isPaid: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  
  subscriptions.push(newSubscription);
  res.status(201).json(newSubscription);
});

app.put('/api/subscriptions/:id', (req, res) => {
  const index = subscriptions.findIndex(s => s.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ error: 'Subscription not found' });
  }
  
  const updatedSubscription = {
    ...subscriptions[index],
    ...req.body,
    updatedAt: new Date().toISOString(),
  };
  
  // Recalculate end date if start date or subscription type changed
  if (req.body.startDate || req.body.subscriptionType) {
    updatedSubscription.endDate = calculateEndDate(
      updatedSubscription.startDate,
      updatedSubscription.subscriptionType
    );
  }
  
  subscriptions[index] = updatedSubscription;
  res.json(updatedSubscription);
});

app.delete('/api/subscriptions/:id', (req, res) => {
  const index = subscriptions.findIndex(s => s.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ error: 'Subscription not found' });
  }
  
  subscriptions.splice(index, 1);
  res.status(204).send();
});

app.get('/api/subscriptions/stats', (req, res) => {
  const updatedSubscriptions = subscriptions.map(updateSubscriptionStatus);
  
  const stats = {
    totalSubscriptions: updatedSubscriptions.length,
    activeSubscriptions: updatedSubscriptions.filter(s => s.status === 'active').length,
    expiredSubscriptions: updatedSubscriptions.filter(s => s.status === 'expired').length,
    totalRevenue: updatedSubscriptions.reduce((sum, s) => sum + s.amount, 0),
    monthlyRevenue: updatedSubscriptions
      .filter(s => s.subscriptionType === 'monthly')
      .reduce((sum, s) => sum + s.amount, 0),
    unpaidInvoices: invoices.filter(i => i.status === 'unpaid').length,
  };
  
  res.json(stats);
});

// Invoices
app.get('/api/invoices', (req, res) => {
  res.json(invoices);
});

app.get('/api/invoices/subscription/:subscriptionId', (req, res) => {
  const subscriptionInvoices = invoices.filter(i => i.subscriptionId === req.params.subscriptionId);
  res.json(subscriptionInvoices);
});

app.post('/api/invoices', (req, res) => {
  const { subscriptionId } = req.body;
  const subscription = subscriptions.find(s => s.id === subscriptionId);
  
  if (!subscription) {
    return res.status(404).json({ error: 'Subscription not found' });
  }
  
  const newInvoice = {
    id: (invoices.length + 1).toString(),
    subscriptionId,
    customerName: subscription.customerName,
    amount: subscription.amount,
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    status: 'unpaid',
    createdAt: new Date().toISOString(),
  };
  
  invoices.push(newInvoice);
  res.status(201).json(newInvoice);
});

app.put('/api/invoices/:id/pay', (req, res) => {
  const index = invoices.findIndex(i => i.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ error: 'Invoice not found' });
  }
  
  invoices[index].status = 'paid';
  
  // Update subscription payment status
  const subscription = subscriptions.find(s => s.id === invoices[index].subscriptionId);
  if (subscription) {
    subscription.isPaid = true;
  }
  
  res.json(invoices[index]);
});

// Notifications
app.get('/api/notifications', (req, res) => {
  res.json(notifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)));
});

app.put('/api/notifications/:id/read', (req, res) => {
  const index = notifications.findIndex(n => n.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ error: 'Notification not found' });
  }
  
  notifications[index].isRead = true;
  res.status(204).send();
});

app.put('/api/notifications/read-all', (req, res) => {
  notifications.forEach(notification => {
    notification.isRead = true;
  });
  res.status(204).send();
});

// Cron job to check for expiring subscriptions and create notifications
cron.schedule('0 9 * * *', () => {
  console.log('Checking for expiring subscriptions...');
  
  const now = new Date();
  subscriptions.forEach(subscription => {
    const endDate = new Date(subscription.endDate);
    const daysUntilExpiry = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
    
    // Create notification for subscriptions expiring in 7 days
    if (daysUntilExpiry === 7) {
      const existingNotification = notifications.find(
        n => n.subscriptionId === subscription.id && n.type === 'expiry_warning'
      );
      
      if (!existingNotification) {
        notifications.push({
          id: (notifications.length + 1).toString(),
          subscriptionId: subscription.id,
          customerName: subscription.customerName,
          type: 'expiry_warning',
          message: `اشتراك ${subscription.customerName} سينتهي خلال 7 أيام`,
          isRead: false,
          createdAt: new Date().toISOString(),
        });
      }
    }
    
    // Create notification for expired subscriptions
    if (daysUntilExpiry === 0) {
      const existingNotification = notifications.find(
        n => n.subscriptionId === subscription.id && n.type === 'expired'
      );
      
      if (!existingNotification) {
        notifications.push({
          id: (notifications.length + 1).toString(),
          subscriptionId: subscription.id,
          customerName: subscription.customerName,
          type: 'expired',
          message: `انتهى اشتراك ${subscription.customerName}`,
          isRead: false,
          createdAt: new Date().toISOString(),
        });
      }
    }
  });
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
