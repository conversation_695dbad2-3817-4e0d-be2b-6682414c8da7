import axios from 'axios';
import { 
  Subscription, 
  CreateSubscriptionData, 
  UpdateSubscriptionData, 
  Invoice, 
  Notification, 
  DashboardStats 
} from '../types/subscription';

const API_BASE_URL = 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Subscriptions API
export const subscriptionAPI = {
  // Get all subscriptions
  getAll: async (): Promise<Subscription[]> => {
    const response = await api.get('/subscriptions');
    return response.data;
  },

  // Get subscription by ID
  getById: async (id: string): Promise<Subscription> => {
    const response = await api.get(`/subscriptions/${id}`);
    return response.data;
  },

  // Create new subscription
  create: async (data: CreateSubscriptionData): Promise<Subscription> => {
    const response = await api.post('/subscriptions', data);
    return response.data;
  },

  // Update subscription
  update: async (data: UpdateSubscriptionData): Promise<Subscription> => {
    const response = await api.put(`/subscriptions/${data.id}`, data);
    return response.data;
  },

  // Delete subscription
  delete: async (id: string): Promise<void> => {
    await api.delete(`/subscriptions/${id}`);
  },

  // Get dashboard stats
  getStats: async (): Promise<DashboardStats> => {
    const response = await api.get('/subscriptions/stats');
    return response.data;
  },
};

// Invoices API
export const invoiceAPI = {
  // Get all invoices
  getAll: async (): Promise<Invoice[]> => {
    const response = await api.get('/invoices');
    return response.data;
  },

  // Get invoices by subscription ID
  getBySubscriptionId: async (subscriptionId: string): Promise<Invoice[]> => {
    const response = await api.get(`/invoices/subscription/${subscriptionId}`);
    return response.data;
  },

  // Create invoice
  create: async (subscriptionId: string): Promise<Invoice> => {
    const response = await api.post('/invoices', { subscriptionId });
    return response.data;
  },

  // Mark invoice as paid
  markAsPaid: async (id: string): Promise<Invoice> => {
    const response = await api.put(`/invoices/${id}/pay`);
    return response.data;
  },
};

// Notifications API
export const notificationAPI = {
  // Get all notifications
  getAll: async (): Promise<Notification[]> => {
    const response = await api.get('/notifications');
    return response.data;
  },

  // Mark notification as read
  markAsRead: async (id: string): Promise<void> => {
    await api.put(`/notifications/${id}/read`);
  },

  // Mark all notifications as read
  markAllAsRead: async (): Promise<void> => {
    await api.put('/notifications/read-all');
  },
};

export default api;
